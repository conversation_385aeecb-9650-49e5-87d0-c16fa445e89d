version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: telebot_postgres_dev
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-telebot}
      POSTGRES_USER: ${POSTGRES_USER:-telebot_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-your_secure_password}
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

volumes:
  postgres_data_dev:
