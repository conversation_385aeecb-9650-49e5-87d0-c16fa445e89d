#!/bin/bash

# Упрощенная установка SSL через acme.sh для Beget VPS

echo "🔒 Настройка SSL через acme.sh"
echo "=============================="

# Загружаем переменные окружения
if [ -f "/etc/edu_telebot/env" ]; then
    source /etc/edu_telebot/env
    echo "✅ Загружены переменные из /etc/edu_telebot/env"
elif [ -f ".env" ]; then
    source .env
    echo "✅ Загружены переменные из .env"
else
    echo "❌ Файл с переменными окружения не найден!"
    echo "Запустите сначала: chmod +x scripts/setup_env.sh && sudo ./scripts/setup_env.sh"
    exit 1
fi

# Проверяем что DOMAIN установлен
if [ -z "$DOMAIN" ] || [ "$DOMAIN" = "your-domain.com" ] || [ "$DOMAIN" = "localhost" ]; then
    echo "❌ Переменная DOMAIN не настроена для SSL!"
    echo "Отредактируйте файл: sudo nano /etc/edu_telebot/env"
    echo "Установите: DOMAIN=ваш-домен.com"
    echo ""
    echo "💡 Для работы без SSL используйте: ./scripts/switch_to_no_ssl.sh"
    exit 1
fi

EMAIL="admin@${DOMAIN}"

echo "🌐 Домен: $DOMAIN"
echo "📧 Email: $EMAIL"
echo ""

# Проверяем доступность порта 80
echo "🔍 Проверяем доступность порта 80..."
if sudo netstat -tlnp | grep -q ":80 "; then
    echo "⚠️ Порт 80 занят. Останавливаем nginx..."
    sudo docker-compose stop nginx 2>/dev/null || true
    sleep 2
fi

# Устанавливаем acme.sh если нужно
echo "📦 Проверяем acme.sh..."
if [ ! -d "$HOME/.acme.sh" ]; then
    echo "Устанавливаем acme.sh..."
    curl https://get.acme.sh | sh -s email=$EMAIL
    source ~/.bashrc
else
    echo "✅ acme.sh уже установлен"
fi

# Создаем директорию для SSL
mkdir -p nginx/ssl

# Получаем сертификат
echo "🔐 Получаем SSL сертификат для $DOMAIN..."
$HOME/.acme.sh/acme.sh --issue -d $DOMAIN --standalone --httpport 80

if [ $? -eq 0 ]; then
    echo "✅ Сертификат получен успешно!"
    
    # Копируем сертификаты
    echo "📋 Копируем сертификаты..."
    $HOME/.acme.sh/acme.sh --install-cert -d $DOMAIN \
        --cert-file $(pwd)/nginx/ssl/cert.pem \
        --key-file $(pwd)/nginx/ssl/privkey.pem \
        --fullchain-file $(pwd)/nginx/ssl/fullchain.pem \
        --reloadcmd "cd $(pwd) && sudo docker-compose restart nginx"
    
    # Устанавливаем права
    chmod 644 nginx/ssl/fullchain.pem nginx/ssl/cert.pem
    chmod 600 nginx/ssl/privkey.pem
    
    # Обновляем конфигурацию nginx для SSL
    echo "⚙️ Обновляем конфигурацию nginx..."
    if [ ! -f "nginx/nginx.conf.backup" ]; then
        cp nginx/nginx.conf nginx/nginx.conf.backup
    fi
    
    # Проверяем, есть ли уже SSL конфигурация
    if grep -q "listen 443 ssl" nginx/nginx.conf; then
        echo "✅ SSL уже настроен в nginx.conf"
        echo "🔧 Обновляем только server_name с доменом: $DOMAIN"

        # Обновляем server_name в существующей конфигурации
        sed -i "s/server_name \${DOMAIN};/server_name $DOMAIN;/g" nginx/nginx.conf
        sed -i "s/server_name _;/server_name $DOMAIN;/g" nginx/nginx.conf

        echo "✅ Конфигурация nginx обновлена с доменом: $DOMAIN"
    else
        echo "⚠️ SSL не настроен в nginx.conf"
        echo "🔧 Ваша конфигурация выглядит хорошо, но нужно добавить SSL"
        echo "💡 Рекомендуется настроить SSL вручную или использовать существующую конфигурацию"

        # Показываем что нужно изменить
        echo ""
        echo "📝 Для ручной настройки SSL в вашей конфигурации:"
        echo "1. Убедитесь что server_name содержит: $DOMAIN"
        echo "2. Проверьте пути к SSL сертификатам:"
        echo "   ssl_certificate /etc/ssl/fullchain.pem;"
        echo "   ssl_certificate_key /etc/ssl/privkey.pem;"
        echo ""

        read -p "Продолжить с текущей конфигурацией? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ Отменено. Отредактируйте nginx/nginx.conf вручную"
            exit 1
        fi

        # Обновляем только server_name в существующей конфигурации
        sed -i "s/server_name \${DOMAIN};/server_name $DOMAIN;/g" nginx/nginx.conf
        sed -i "s/server_name _;/server_name $DOMAIN;/g" nginx/nginx.conf

        echo "✅ Обновлен server_name в существующей конфигурации"
    fi

    # Пропускаем создание новой конфигурации
    echo "📋 Сохраняем вашу существующую nginx конфигурацию"

    
    # Обновляем переменные окружения
    echo "📝 Обновляем переменные окружения..."
    sudo sed -i "s|WEBHOOK_HOST=.*|WEBHOOK_HOST=https://$DOMAIN|" /etc/edu_telebot/env
    sudo sed -i "s|WEBHOOK_MODE=.*|WEBHOOK_MODE=true|" /etc/edu_telebot/env
    sudo sed -i "s|DOMAIN=.*|DOMAIN=$DOMAIN|" /etc/edu_telebot/env
    
    # Запускаем nginx
    echo "▶️ Запускаем nginx с SSL..."
    sudo docker-compose up -d nginx
    
    # Автообновление через acme.sh (встроенное, без crontab)
    echo "⏰ acme.sh автоматически настроит обновление сертификатов..."
    echo "💡 acme.sh использует встроенный механизм обновления без crontab"
    
    echo ""
    echo "🎉 SSL настроен успешно!"
    echo "🌐 HTTPS URL: https://$DOMAIN/webhook"
    echo "🔄 Автообновление: встроено в acme.sh (без crontab)"
    echo ""
    echo "🧪 Тестируем:"
    echo "curl -I https://$DOMAIN/health"
    
else
    echo "❌ Ошибка получения сертификата"
    echo "💡 Убедитесь что:"
    echo "   - Домен $DOMAIN указывает на этот сервер"
    echo "   - Порт 80 открыт и доступен"
    echo "   - Нет других процессов на порту 80"
    
    # Возвращаем nginx
    sudo docker-compose up -d nginx
    exit 1
fi
